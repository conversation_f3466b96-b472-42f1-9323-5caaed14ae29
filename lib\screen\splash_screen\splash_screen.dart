import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:ratulive/common/widget/custom_shimmer_fill_text.dart';
import 'package:ratulive/common/widget/theme_blur_bg.dart';
import 'package:ratulive/screen/splash_screen/splash_screen_controller.dart';
import 'package:ratulive/utilities/app_res.dart';
import 'package:ratulive/utilities/text_style_custom.dart';
import 'package:ratulive/utilities/theme_res.dart';
import 'dart:ui' as ui;

class SplashScreen extends StatelessWidget {
  const SplashScreen({super.key});

  @override
  Widget build(BuildContext context) {
    // Initialize the controller
    final controller = Get.put(SplashScreenController());
    
    return Scaffold(
      backgroundColor: Colors.black, // Ensure immediate black background 
      body: Stack(
        children: [
          const ThemeBlurBg(),
          // Logo image at center
          Center(
            child: Image.asset(
              'assets/images/logo-home2.png',
              width: 120,
              height: 120,
            ),
          ),
          // App name below logo
          Align(
            alignment: const Alignment(0, 0.3),
            child: CustomShimmerFillText(
              text: AppRes.appName.toUpperCase(),
              baseColor: whitePure(context),
              textStyle: TextStyleCustom.unboundedBlack900(
                  color: whitePure(context), fontSize: 30),
              finalColor: whitePure(context),
              shimmerColor: themeAccentSolid(context),
            ),
          ),
          // Loading indicator at bottom
          Align(
            alignment: const Alignment(0, 0.7),
            child: SizedBox(
              width: 40,
              height: 40,
              child: CircularProgressIndicator(
                valueColor: AlwaysStoppedAnimation<Color>(themeAccentSolid(context)),
                strokeWidth: 3,
              ),
            ),
          ),
        ],
      ),
    );
  }
}
