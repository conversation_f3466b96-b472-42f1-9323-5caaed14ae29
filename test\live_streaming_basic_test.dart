import 'package:flutter_test/flutter_test.dart';
import 'package:get/get.dart';
import 'package:ratulive/screen/live_stream/livestream_screen/livestream_screen_controller.dart';
import 'package:ratulive/screen/live_stream/create_live_stream_screen/create_live_stream_screen_controller.dart';
import 'package:ratulive/model/livestream/livestream.dart';
import 'package:ratulive/model/livestream/livestream_user_state.dart';

void main() {
  group('Live Streaming Basic Tests', () {
    setUp(() {
      Get.testMode = true;
    });

    tearDown(() {
      Get.reset();
    });

    group('Controller Initialization Tests', () {
      test('LivestreamScreenController should initialize correctly', () {
        // Arrange
        final livestream = Livestream(roomID: 'test_room');
        
        // Act
        final controller = LivestreamScreenController(livestream.obs, true);

        // Assert
        expect(controller.isHost, true);
        expect(controller.liveData.value.roomID, 'test_room');
        expect(controller.isFrontCamera, true);
      });

      test('CreateLiveStreamScreenController should initialize correctly', () {
        // Act
        final controller = CreateLiveStreamScreenController();

        // Assert
        expect(controller.isFrontCamera, true);
        expect(controller.isRestricted.value, false);
        expect(controller.localViewID.value, -1);
      });
    });

    group('Camera State Management Tests', () {
      test('Camera toggle should update front camera state', () {
        // Arrange
        final livestream = Livestream(roomID: 'test_room');
        final controller = LivestreamScreenController(livestream.obs, true);
        final initialState = controller.isFrontCamera;

        // Act
        // Note: This will fail in test environment due to ZegoEngine dependency
        // but we can test the state management logic
        expect(() => controller.toggleCamera(), returnsNormally);

        // The state should be reverted due to the error handling we added
        expect(controller.isFrontCamera, initialState);
      });

      test('Video toggle should handle null state gracefully', () {
        // Arrange
        final livestream = Livestream(roomID: 'test_room');
        final controller = LivestreamScreenController(livestream.obs, true);

        // Act & Assert
        expect(() => controller.toggleVideo(null), returnsNormally);
      });

      test('Microphone toggle should handle null state gracefully', () {
        // Arrange
        final livestream = Livestream(roomID: 'test_room');
        final controller = LivestreamScreenController(livestream.obs, true);

        // Act & Assert
        expect(() => controller.toggleMic(null), returnsNormally);
      });
    });

    group('User State Tests', () {
      test('Should handle user state with video disabled by host', () {
        // Arrange
        final livestream = Livestream(roomID: 'test_room');
        final controller = LivestreamScreenController(livestream.obs, false);
        
        final userState = LivestreamUserState(
          type: LivestreamUserType.audience,
          userId: 123,
          liveCoin: 0,
          currentBattleCoin: 0,
          totalBattleCoin: 0,
          followersGained: [],
          joinStreamTime: 0,
          videoStatus: VideoAudioStatus.offByHost,
        );

        // Act & Assert
        // Should not crash and should show appropriate message
        expect(() => controller.toggleVideo(userState), returnsNormally);
      });

      test('Should handle user state with audio disabled by host', () {
        // Arrange
        final livestream = Livestream(roomID: 'test_room');
        final controller = LivestreamScreenController(livestream.obs, false);
        
        final userState = LivestreamUserState(
          type: LivestreamUserType.audience,
          userId: 123,
          liveCoin: 0,
          currentBattleCoin: 0,
          totalBattleCoin: 0,
          followersGained: [],
          joinStreamTime: 0,
          audioStatus: VideoAudioStatus.offByHost,
        );

        // Act & Assert
        // Should not crash and should show appropriate message
        expect(() => controller.toggleMic(userState), returnsNormally);
      });
    });

    group('Error Handling Tests', () {
      test('Should handle empty room ID gracefully', () {
        // Arrange
        final livestream = Livestream(); // No room ID
        final controller = LivestreamScreenController(livestream.obs, true);

        // Act & Assert
        expect(() => controller.startHostPublish(), returnsNormally);
      });

      test('Should handle video player with empty URL', () {
        // Arrange
        final livestream = Livestream(dummyUserLink: '');
        final controller = LivestreamScreenController(livestream.obs, false);

        // Act & Assert
        expect(() => controller.initVideoPlayer(), returnsNormally);
      });

      test('Should handle stop preview without errors', () {
        // Arrange
        final controller = CreateLiveStreamScreenController();

        // Act & Assert
        expect(() => controller.stopPreview(), returnsNormally);
      });
    });

    group('State Validation Tests', () {
      test('Should validate camera state before operations', () {
        // Arrange
        final livestream = Livestream(roomID: 'test_room');
        final controller = LivestreamScreenController(livestream.obs, true);

        // Create user state with video disabled
        final userState = LivestreamUserState(
          type: LivestreamUserType.host,
          userId: 123,
          liveCoin: 0,
          currentBattleCoin: 0,
          totalBattleCoin: 0,
          followersGained: [],
          joinStreamTime: 0,
          videoStatus: VideoAudioStatus.offByMe,
        );

        controller.liveUsersStates.add(userState);

        // Act & Assert
        // Should handle disabled camera state appropriately
        expect(() => controller.toggleFlipCamera(), returnsNormally);
      });
    });

    group('Reactive State Tests', () {
      test('Should maintain reactive state correctly', () {
        // Arrange
        final livestream = Livestream(roomID: 'test_room');
        final controller = LivestreamScreenController(livestream.obs, true);

        // Assert
        expect(controller.liveData, isA<Rx<Livestream>>());
        expect(controller.isPlayerMute, isA<RxBool>());
        expect(controller.isMinViewerTimeout, isA<RxBool>());
        expect(controller.isTextEmpty, isA<RxBool>());
      });

      test('Create controller should maintain reactive state', () {
        // Arrange
        final controller = CreateLiveStreamScreenController();

        // Assert
        expect(controller.isRestricted, isA<RxBool>());
        expect(controller.localView, isA<Rx>());
        expect(controller.localViewID, isA<RxInt>());
      });
    });
  });
}
